// Public G2G Price Updater - Minimal code visible to sheet users
// This script only receives data and updates the sheet - no business logic exposed

// Configuration
const SHEET_ID = '1FStcYoFPninNRsWX4XvXxHVtrc2usOnJ79R-ep3l38c';
const SHEET_NAME = 'Sheet1';
const CATA_SHEET_NAME = 'Cata';
const PRICE_ROW = 4;
const CATA_START_ROW = 6;

// Simple realm column mapping
const REALM_COLUMNS = {
  'spine a': 2,
  'spine h': 3,
  'living a': 4,
  'living h': 5,
  'thunder a': 6,
  'thunder h': 7,
  'wild a': 8
};

// Colors
const GREEN_COLOR = '#00FF00';
const RED_COLOR = '#FF0000';

/**
 * Handle POST requests from private script or Chrome extension
 */
function doPost(e) {
  try {
    console.log('Received POST request');

    let requestData;
    try {
      requestData = JSON.parse(e.postData.contents);
    } catch (parseError) {
      console.error('Error parsing request data:', parseError);
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Invalid JSON data'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }

    console.log('Request data:', requestData);

    if (requestData.action === 'updatePrices') {
      const result = updateSheetPrices(requestData.data);
      return ContentService
        .createTextOutput(JSON.stringify({
          success: result.success,
          message: result.message,
          timestamp: new Date().toISOString()
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }

    if (requestData.action === 'updateCataData') {
      const result = updateCataData(requestData.data);
      return ContentService
        .createTextOutput(JSON.stringify({
          success: result.success,
          message: result.message,
          timestamp: new Date().toISOString()
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }

    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Unknown action'
      }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    console.error('Error in doPost:', error);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle GET requests
 */
function doGet(e) {
  return ContentService
    .createTextOutput(JSON.stringify({
      status: 'G2G Price Updater is running',
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Update sheet with price data (simplified version)
 */
function updateSheetPrices(priceData) {
  try {
    console.log('Updating sheet with price data:', priceData);

    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${SHEET_NAME}" not found`);
    }

    // Get dollar rate from A2
    const dollarRate = sheet.getRange(2, 1).getValue();
    console.log('Dollar rate from A2:', dollarRate);

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in A2');
    }

    let updatedCount = 0;
    let offCount = 0;

    // Process each realm
    for (const [realmKey, usdPrice] of Object.entries(priceData)) {
      const columnIndex = REALM_COLUMNS[realmKey.toLowerCase()];

      if (!columnIndex) {
        console.warn(`Unknown realm key: ${realmKey}`);
        continue;
      }

      const cell = sheet.getRange(PRICE_ROW, columnIndex);

      if (usdPrice !== null && usdPrice !== undefined && usdPrice > 0) {
        const discountedPrice = usdPrice * 0.91;
        const convertedPrice = Math.round(discountedPrice * dollarRate);

        cell.setValue(convertedPrice);
        cell.setBackground(GREEN_COLOR);
        cell.setFontColor('#000000');
        updatedCount++;
        console.log(`Updated ${realmKey}: USD ${usdPrice} → ${convertedPrice}`);
      } else {
        cell.setValue('OFF');
        cell.setBackground(RED_COLOR);
        cell.setFontColor('#FFFFFF');
        offCount++;
        console.log(`Set ${realmKey} to OFF`);
      }
    }

    // Update timestamp
    if (updatedCount > 0) {
      const now = new Date();
      const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
      const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
      const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                        tehranTime.getMinutes().toString().padStart(2, '0');

      const timestampCell = sheet.getRange(PRICE_ROW, 1);
      timestampCell.setValue(timeString);
      timestampCell.setFontSize(10);
      timestampCell.setFontColor('#000000');
      timestampCell.setBackground('#E8F5E8');
    }

    const message = `Updated ${updatedCount} prices, ${offCount} set to OFF`;
    console.log(message);

    return {
      success: true,
      message: message,
      updatedCount: updatedCount,
      offCount: offCount
    };

  } catch (error) {
    console.error('Error updating sheet:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

/**
 * Update Cata sheet with pre-processed data from private script
 */
function updateCataData(cataData) {
  try {
    console.log('Updating Cata sheet with processed data');

    const cataSheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);
    if (!cataSheet) {
      throw new Error(`Cata sheet "${CATA_SHEET_NAME}" not found`);
    }

    let totalUpdated = 0;

    // Update each realm column with pre-formatted data
    for (const [realmKey, realmData] of Object.entries(cataData.realms || {})) {
      if (realmData.values && realmData.values.length > 0) {
        const column = realmData.column;
        const startRow = CATA_START_ROW;

        // Apply the pre-formatted data
        const dataRange = cataSheet.getRange(startRow, column, realmData.values.length, 1);
        dataRange.setValues(realmData.values);

        if (realmData.backgrounds) {
          dataRange.setBackgrounds(realmData.backgrounds);
        }

        if (realmData.fontWeights) {
          dataRange.setFontWeights(realmData.fontWeights);
        }

        // Apply standard formatting
        dataRange.setFontFamily('Consolas');
        dataRange.setFontSize(9);
        dataRange.setVerticalAlignment('middle');
        dataRange.setHorizontalAlignment('left');
        dataRange.setFontColor('#000000');
        dataRange.setBorder(true, true, true, true, true, true, '#CCCCCC', SpreadsheetApp.BorderStyle.SOLID);

        totalUpdated += realmData.values.length;
        console.log(`Updated ${realmKey}: ${realmData.values.length} entries`);
      }
    }

    // Update timestamp if provided
    if (cataData.timestamp) {
      const timestampCell = cataSheet.getRange(1, 1);
      timestampCell.setValue(cataData.timestamp);
      timestampCell.setFontSize(12);
      timestampCell.setFontColor('#333333');
      timestampCell.setFontWeight('bold');
    }

    // Update recommended positions if provided
    if (cataData.positions) {
      for (const [realmKey, positionData] of Object.entries(cataData.positions)) {
        const cell = cataSheet.getRange(4, positionData.column);
        cell.setValue(positionData.value);
        cell.setBackground(positionData.background);
        cell.setFontColor(positionData.fontColor);
        cell.setFontWeight(positionData.fontWeight);
      }
    }

    const message = `Cata data updated: ${totalUpdated} total entries`;
    console.log(message);

    return {
      success: true,
      message: message,
      totalUpdated: totalUpdated
    };

  } catch (error) {
    console.error('Error updating Cata data:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

/**
 * Simple manual trigger for testing
 */
function testPublicUpdate() {
  const sampleData = {
    'spine h': 0.001234,
    'spine a': null,
    'living a': 0.001456
  };

  const result = updateSheetPrices(sampleData);
  console.log('Test result:', result);
  return result;
}

/**
 * Manual function to trigger Cata update (calls the original function)
 */
function updateCataReports() {
  // This function exists to maintain compatibility with existing triggers
  // It will call the original updateCataReports function from code.gs
  console.log('Manual Cata update triggered - calling original function');

  try {
    // Call the original function that's still in code.gs
    return updateCataReportsOriginal();
  } catch (error) {
    console.error('Error in manual Cata update:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}