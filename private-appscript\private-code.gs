// Private G2G Price Updater - Hidden from main sheet users
// This script runs independently and pushes data to the main sheet

// Configuration - UPDATE THESE WITH YOUR ACTUAL SHEET IDs
const MAIN_SHEET_ID = '1FStcYoFPninNRsWX4XvXxHVtrc2usOnJ79R-ep3l38c'; // Your main sheet ID
const MAIN_SHEET_NAME = 'Sheet1';
const CATA_SHEET_NAME = 'Cata';

// Retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 2000;
const API_DELAY_MS = 1000; // Delay between API calls

// Error tracking
let errorCount = 0;
const MAX_ERRORS_BEFORE_ALERT = 5;

// Realm configurations (same as original)
const REALM_COLUMNS = {
  'spine a': 2,
  'spine h': 3,
  'living a': 4,
  'living h': 5,
  'thunder a': 6,
  'thunder h': 7,
  'wild a': 8
};

const CATA_REALMS = {
  'firemaw a': {
    name: 'Firemaw Alliance',
    url: 'https://sls.g2g.com/offer/search?seo_term=wow-classic-gold&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41012&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41012&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 2
  },
  'gehennas h': {
    name: 'Gehennas Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41023&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41023&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 3
  },
  'golemagg h': {
    name: 'Golemagg Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41029&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41029&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 4
  },
  'everlook a': {
    name: 'Everlook Alliance',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41003&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41003&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 5
  },
  'venoxis h': {
    name: 'Venoxis Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41122&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41122&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 6
  },
  'pyrewood village a': {
    name: 'Pyrewood Village Alliance',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41083&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41083&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 7
  }
};

// Colors for formatting
const GREEN_COLOR = '#00FF00';
const RED_COLOR = '#FF0000';
const YELLOW_COLOR = '#FFFF00';
const LIGHT_GRAY = '#F5F5F5';
const IRANIAN_HIGHLIGHT = '#E6B3B3';

/**
 * Enhanced error handling with retry logic
 */
function withRetry(operation, operationName, maxRetries = MAX_RETRIES) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`${operationName} - Attempt ${attempt}/${maxRetries}`);
      const result = operation();
      if (attempt > 1) {
        console.log(`${operationName} succeeded on attempt ${attempt}`);
      }
      return result;
    } catch (error) {
      console.error(`${operationName} failed on attempt ${attempt}:`, error);

      if (attempt === maxRetries) {
        console.error(`${operationName} failed after ${maxRetries} attempts`);
        errorCount++;
        throw error;
      }

      // Wait before retry
      Utilities.sleep(RETRY_DELAY_MS * attempt);
    }
  }
}

/**
 * Safe API fetch with enhanced error handling
 */
function safeFetch(url, options = {}) {
  const defaultOptions = {
    muteHttpExceptions: true,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
  };

  const mergedOptions = { ...defaultOptions, ...options };

  return withRetry(() => {
    const response = UrlFetchApp.fetch(url, mergedOptions);

    if (response.getResponseCode() !== 200) {
      throw new Error(`HTTP ${response.getResponseCode()}: ${response.getContentText()}`);
    }

    return response;
  }, `API fetch to ${url.substring(0, 50)}...`);
}

/**
 * Enhanced realm data fetching with better error handling
 */
function fetchRealmDataSafe(url, pageSize = 20) {
  console.log(`Fetching realm data: ${url.substring(0, 100)}...`);

  // Add page_size parameter
  if (!url.includes('page_size=')) {
    url += (url.includes('?') ? '&' : '?') + `page_size=${pageSize}`;
  } else {
    url = url.replace(/page_size=\d+/, `page_size=${pageSize}`);
  }

  const response = safeFetch(url);
  const data = JSON.parse(response.getContentText());

  if (data.code !== 2000 || !data.payload || !data.payload.results) {
    throw new Error(`Invalid API response: code=${data.code}, payload=${!!data.payload}`);
  }

  // Sort by price
  data.payload.results.sort((a, b) => {
    const priceA = a.converted_unit_price || 999;
    const priceB = b.converted_unit_price || 999;
    return priceA - priceB;
  });

  console.log(`Successfully fetched ${data.payload.results.length} results`);
  return data.payload.results;
}

/**
 * Main function to fetch prices and update the main sheet
 */
function updateMainSheetPrices() {
  console.log('=== Starting Main Sheet Price Update ===');

  try {
    // Fetch prices for all realms
    const priceData = {};

    // For regular realms (spine, living, thunder, wild)
    for (const [realmKey, columnIndex] of Object.entries(REALM_COLUMNS)) {
      try {
        console.log(`Fetching price for ${realmKey}...`);

        // This would need to be implemented based on your specific API endpoints
        // For now, using a placeholder - you'll need to add the actual API URLs
        const price = await fetchRealmPrice(realmKey);
        priceData[realmKey] = price;

        // Delay between API calls
        Utilities.sleep(API_DELAY_MS);

      } catch (error) {
        console.error(`Failed to fetch price for ${realmKey}:`, error);
        priceData[realmKey] = null; // Will show as OFF
      }
    }

    // Update the main sheet
    const updateResult = updateSheetPricesSecure(priceData);

    if (updateResult.success) {
      console.log(`✓ Successfully updated main sheet: ${updateResult.message}`);
      errorCount = 0; // Reset error count on success
    } else {
      throw new Error(updateResult.message);
    }

    return {
      success: true,
      message: `Updated ${updateResult.updatedCount} prices, ${updateResult.offCount} set to OFF`,
      data: priceData
    };

  } catch (error) {
    console.error('Error in updateMainSheetPrices:', error);
    errorCount++;

    // Send alert if too many errors
    if (errorCount >= MAX_ERRORS_BEFORE_ALERT) {
      sendErrorAlert(error);
    }

    return {
      success: false,
      message: error.toString(),
      errorCount: errorCount
    };
  }
}

/**
 * Secure function to update sheet prices (similar to original but with better error handling)
 */
function updateSheetPricesSecure(priceData) {
  return withRetry(() => {
    console.log('Updating main sheet with price data:', priceData);

    const sheet = SpreadsheetApp.openById(MAIN_SHEET_ID).getSheetByName(MAIN_SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${MAIN_SHEET_NAME}" not found`);
    }

    // Get dollar rate from A2
    const dollarRate = sheet.getRange(2, 1).getValue();
    console.log('Dollar rate from A2:', dollarRate);

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in A2. Please ensure A2 contains a valid number.');
    }

    let updatedCount = 0;
    let offCount = 0;

    // Process each realm
    for (const [realmKey, usdPrice] of Object.entries(priceData)) {
      const columnIndex = REALM_COLUMNS[realmKey.toLowerCase()];

      if (!columnIndex) {
        console.warn(`Unknown realm key: ${realmKey}`);
        continue;
      }

      const cell = sheet.getRange(4, columnIndex); // PRICE_ROW = 4

      if (usdPrice !== null && usdPrice !== undefined && usdPrice > 0) {
        // Convert price: (USD price - 9%) × Dollar rate, rounded
        const discountedPrice = usdPrice * 0.91;
        const convertedPrice = Math.round(discountedPrice * dollarRate);

        cell.setValue(convertedPrice);
        cell.setBackground(GREEN_COLOR);
        cell.setFontColor('#000000');
        updatedCount++;
        console.log(`Updated ${realmKey}: USD ${usdPrice} → ${convertedPrice} (after -9% and ×${dollarRate})`);
      } else {
        // No price available - set to OFF
        cell.setValue('OFF');
        cell.setBackground(RED_COLOR);
        cell.setFontColor('#FFFFFF');
        offCount++;
        console.log(`Set ${realmKey} to OFF (no price available)`);
      }
    }

    // Update timestamp in A4 - Tehran time
    if (updatedCount > 0) {
      const now = new Date();
      const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
      const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
      const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                        tehranTime.getMinutes().toString().padStart(2, '0');

      const timestampCell = sheet.getRange(4, 1);
      timestampCell.setValue(timeString);
      timestampCell.setFontSize(10);
      timestampCell.setFontColor('#000000');
      timestampCell.setBackground('#E8F5E8');
    }

    const message = `Updated ${updatedCount} prices, ${offCount} set to OFF`;
    console.log(message);

    return {
      success: true,
      message: message,
      updatedCount: updatedCount,
      offCount: offCount
    };

  }, 'Update main sheet prices');
}

/**
 * Enhanced Cata reports update with better reliability
 */
function updateCataReportsSecure() {
  console.log('=== Starting Secure Cata Reports Update ===');

  try {
    const spreadsheet = SpreadsheetApp.openById(MAIN_SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);
    const mainSheet = spreadsheet.getSheetByName(MAIN_SHEET_NAME);

    if (!cataSheet) {
      throw new Error(`Cata sheet "${CATA_SHEET_NAME}" not found`);
    }

    // Get settings with defaults
    const pageSize = cataSheet.getRange(3, 1).getValue() || 20;
    const undercutThreshold = cataSheet.getRange(5, 1).getValue() || 0;
    const updateInterval = cataSheet.getRange(7, 1).getValue() || 60;
    const dollarRate = mainSheet.getRange(2, 1).getValue();

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in main sheet A2');
    }

    console.log(`Settings: pageSize=${pageSize}, dollarRate=${dollarRate}, threshold=${undercutThreshold}`);

    let totalUpdated = 0;
    let successfulRealms = 0;

    // Process each realm with individual error handling
    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      try {
        console.log(`Processing ${realmKey}...`);

        const realmData = fetchRealmDataSafe(realmConfig.url, pageSize);
        const updatedCount = updateRealmColumnSecure(cataSheet, realmConfig.column, realmData, dollarRate, undercutThreshold);

        totalUpdated += updatedCount;
        successfulRealms++;

        console.log(`✓ ${realmKey}: Updated ${updatedCount} entries`);

        // Delay between realms to prevent rate limiting
        Utilities.sleep(API_DELAY_MS);

      } catch (error) {
        console.error(`✗ Error processing ${realmKey}:`, error);
        // Continue with other realms even if one fails
      }
    }

    // Update timestamp
    const now = new Date();
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
    const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
    const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                      tehranTime.getMinutes().toString().padStart(2, '0');

    const timestampCell = cataSheet.getRange(1, 1);
    timestampCell.setValue(timeString);
    timestampCell.setFontSize(12);
    timestampCell.setFontColor('#333333');
    timestampCell.setFontWeight('bold');

    // Update recommended positions
    updateRecommendedPositionsSecure();

    // Setup auto update if interval > 0
    if (updateInterval > 0) {
      setupAutoUpdateSecure(updateInterval);
    }

    const message = `Cata reports updated: ${totalUpdated} entries across ${successfulRealms}/${Object.keys(CATA_REALMS).length} realms`;
    console.log(message);

    // Reset error count on successful update
    if (successfulRealms > 0) {
      errorCount = 0;
    }

    return {
      success: true,
      message: message,
      totalUpdated: totalUpdated,
      successfulRealms: successfulRealms
    };

  } catch (error) {
    console.error('Error in updateCataReportsSecure:', error);
    errorCount++;

    if (errorCount >= MAX_ERRORS_BEFORE_ALERT) {
      sendErrorAlert(error);
    }

    return {
      success: false,
      message: error.toString(),
      errorCount: errorCount
    };
  }
}

/**
 * Secure realm column update with enhanced error handling
 */
function updateRealmColumnSecure(sheet, column, realmData, dollarRate, undercutThreshold = 0) {
  return withRetry(() => {
    const startRow = 6; // CATA_START_ROW

    if (!realmData || realmData.length === 0) {
      console.log(`No data for column ${column}`);
      return 0;
    }

    console.log(`Updating column ${column} with ${realmData.length} entries`);

    // Get Iranian shops list
    const iranianShops = getIranianShopsSecure(sheet);

    // Find my shops and their prices for undercut detection
    const myShopPrices = [];
    for (const seller of realmData) {
      const shopName = (seller.username || '').toLowerCase();
      if (shopName.includes('bonyadi') || shopName.includes('miba')) {
        const usdPrice = seller.converted_unit_price || 0;
        const convertedPrice = (usdPrice * 0.91) * dollarRate;
        myShopPrices.push(convertedPrice);
      }
    }

    // Prepare batch data
    const values = [];
    const backgrounds = [];
    const fontWeights = [];

    for (let i = 0; i < realmData.length; i++) {
      const seller = realmData[i];
      const shopName = seller.username || 'Unknown';
      const stock = seller.available_qty || 0;
      const usdPrice = seller.converted_unit_price || 0;
      const convertedPrice = (usdPrice * 0.91) * dollarRate;

      const formattedShop = shopName.substring(0, 7).padEnd(7, ' ');
      const formattedStock = formatStock(stock).padStart(6, ' ');
      const formattedUSD = usdPrice.toFixed(6);
      const formattedConverted = formatConvertedPrice(convertedPrice);

      const displayText = `${formattedShop}|${formattedStock}|${formattedUSD}|${formattedConverted}`;
      values.push([displayText]);

      let backgroundColor = null;
      let fontWeight = 'normal';
      const shopNameLower = shopName.toLowerCase();

      if (shopNameLower.includes('bonyadi') || shopNameLower.includes('miba')) {
        backgroundColor = YELLOW_COLOR;
        fontWeight = 'bold';
      } else if (isIranianShopSecure(shopName, iranianShops)) {
        backgroundColor = IRANIAN_HIGHLIGHT;
        fontWeight = 'bold';
      } else if (undercutThreshold > 0 && myShopPrices.length > 0) {
        const isUndercutting = myShopPrices.some(myPrice =>
          convertedPrice < myPrice && (myPrice - convertedPrice) <= undercutThreshold
        );
        if (isUndercutting) {
          backgroundColor = '#FFB6C1';
        }
      }

      if (!backgroundColor) {
        backgroundColor = (i % 2 === 0) ? '#FFFFFF' : LIGHT_GRAY;
      }

      backgrounds.push([backgroundColor]);
      fontWeights.push([fontWeight]);
    }

    // Apply updates in batch
    if (values.length > 0) {
      const dataRange = sheet.getRange(startRow, column, values.length, 1);
      dataRange.setValues(values);
      dataRange.setBackgrounds(backgrounds);
      dataRange.setFontWeights(fontWeights);
      dataRange.setFontFamily('Consolas');
      dataRange.setFontSize(9);
      dataRange.setVerticalAlignment('middle');
      dataRange.setHorizontalAlignment('left');
      dataRange.setFontColor('#000000');
      dataRange.setBorder(true, true, true, true, true, true, '#CCCCCC', SpreadsheetApp.BorderStyle.SOLID);

      // Apply rich text formatting for converted prices
      for (let i = 0; i < values.length; i++) {
        const cellRange = sheet.getRange(startRow + i, column);
        const text = values[i][0];
        const parts = text.split('|');
        if (parts.length === 4) {
          const richText = SpreadsheetApp.newRichTextValue()
            .setText(text)
            .setTextStyle(0, text.lastIndexOf('|'), SpreadsheetApp.newTextStyle().setForegroundColor('#000000').build())
            .setTextStyle(text.lastIndexOf('|') + 1, text.length, SpreadsheetApp.newTextStyle().setForegroundColor('#008000').build())
            .build();
          cellRange.setRichTextValue(richText);
        }
      }

      // Clear old data below
      const clearStartRow = startRow + values.length;
      const clearEndRow = startRow + 100;
      if (clearStartRow <= clearEndRow) {
        const clearRange = sheet.getRange(clearStartRow, column, clearEndRow - clearStartRow + 1, 1);
        clearRange.clearContent();
        clearRange.clearFormat();
      }
    }

    return realmData.length;
  }, `Update realm column ${column}`);
}

/**
 * Utility functions (secure versions)
 */
function getIranianShopsSecure(sheet) {
  try {
    const iranianShops = [];
    for (let row = 19; row <= 28; row++) {
      const shopName = sheet.getRange(row, 1).getValue();
      if (shopName && typeof shopName === 'string' && shopName.trim()) {
        iranianShops.push(shopName.trim().toLowerCase());
      }
    }
    return iranianShops;
  } catch (error) {
    console.error('Error getting Iranian shops:', error);
    return [];
  }
}

function isIranianShopSecure(shopName, iranianShops) {
  if (!shopName || !iranianShops.length) return false;
  const cleanShopName = shopName.toLowerCase().trim();
  return iranianShops.some(iranianShop => {
    return cleanShopName === iranianShop ||
           cleanShopName.includes(iranianShop) ||
           iranianShop.includes(cleanShopName);
  });
}

function formatStock(stock) {
  if (stock >= 1000000) {
    return (stock / 1000000).toFixed(1) + 'M';
  } else if (stock >= 1000) {
    return (stock / 1000).toFixed(1) + 'K';
  } else {
    return stock.toString();
  }
}

function formatConvertedPrice(price) {
  return (price / 1000).toFixed(1) + 'K';
}

/**
 * Enhanced recommended positions update
 */
function updateRecommendedPositionsSecure() {
  try {
    console.log('Updating recommended positions...');
    const cataSheet = SpreadsheetApp.openById(MAIN_SHEET_ID).getSheetByName(CATA_SHEET_NAME);

    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      if (!realmConfig.recommendedUrl) continue;

      try {
        console.log(`Getting position for ${realmKey}...`);
        const position = getMibaPositionSecure(realmConfig.recommendedUrl);
        const cell = cataSheet.getRange(4, realmConfig.column);

        if (position) {
          cell.setValue(position);
          cell.setBackground(getPositionColor(position));
          cell.setFontColor('#FFFFFF');
          cell.setFontWeight('bold');
          console.log(`${realmKey}: position ${position}`);
        } else {
          cell.setValue('N/A');
          cell.setBackground('#CCCCCC');
          cell.setFontColor('#000000');
          console.log(`${realmKey}: miba not found`);
        }

        Utilities.sleep(500);
      } catch (error) {
        console.error(`Error getting position for ${realmKey}:`, error);
      }
    }

    console.log('Recommended positions updated');
    return true;
  } catch (error) {
    console.error('Error updating recommended positions:', error);
    return false;
  }
}

function getMibaPositionSecure(recommendedUrl) {
  try {
    const response = safeFetch(recommendedUrl);
    const data = JSON.parse(response.getContentText());

    if (data.code !== 2000 || !data.payload || !data.payload.results) {
      console.error('Bad recommended API response');
      return null;
    }

    for (let i = 0; i < data.payload.results.length; i++) {
      const username = (data.payload.results[i].username || '').toLowerCase();
      if (username.includes('miba')) {
        return i + 1;
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting miba position:', error);
    return null;
  }
}

function getPositionColor(position) {
  if (!position || position > 10) {
    return '#CC0000';
  }
  const ratio = (10 - position) / 9;
  const red = Math.round(204 * (1 - ratio));
  const green = Math.round(204 * ratio);
  return `rgb(${red}, ${green}, 0)`;
}

/**
 * Enhanced auto-update setup with better trigger management
 */
function setupAutoUpdateSecure(intervalSeconds) {
  try {
    // Delete old triggers
    const triggers = ScriptApp.getProjectTriggers();
    triggers.forEach(trigger => {
      if (trigger.getHandlerFunction() === 'autoUpdateCataSecure') {
        ScriptApp.deleteTrigger(trigger);
      }
    });

    // Create new trigger - minimum 1 minute
    const intervalMinutes = Math.max(1, Math.floor(intervalSeconds / 60));
    ScriptApp.newTrigger('autoUpdateCataSecure')
      .timeBased()
      .everyMinutes(intervalMinutes)
      .create();

    console.log(`Auto-update trigger set for every ${intervalMinutes} minutes`);
  } catch (error) {
    console.error('Error setting up auto-update:', error);
  }
}

/**
 * Auto-update function called by trigger
 */
function autoUpdateCataSecure() {
  console.log('=== Auto Update Triggered ===');
  updateCataReportsSecure();
}

/**
 * Error alerting system
 */
function sendErrorAlert(error) {
  try {
    console.error(`ALERT: Too many errors (${errorCount}). Last error:`, error);

    // You can implement email alerts here if needed
    // MailApp.sendEmail({
    //   to: '<EMAIL>',
    //   subject: 'G2G Price Updater - Multiple Errors',
    //   body: `The G2G Price Updater has encountered ${errorCount} consecutive errors.\n\nLast error: ${error.toString()}\n\nPlease check the script.`
    // });

  } catch (alertError) {
    console.error('Error sending alert:', alertError);
  }
}

/**
 * Main setup function
 */
function setupPrivateScript() {
  console.log('=== Setting up Private Script ===');

  try {
    // Test sheet access
    const spreadsheet = SpreadsheetApp.openById(MAIN_SHEET_ID);
    const mainSheet = spreadsheet.getSheetByName(MAIN_SHEET_NAME);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);

    if (!mainSheet || !cataSheet) {
      throw new Error('Cannot access required sheets');
    }

    console.log('✓ Sheet access verified');

    // Setup initial auto-update
    const updateInterval = cataSheet.getRange(7, 1).getValue() || 60;
    setupAutoUpdateSecure(updateInterval);

    console.log('✓ Auto-update triggers configured');
    console.log('✓ Private script setup complete');

    return true;
  } catch (error) {
    console.error('Setup failed:', error);
    return false;
  }
}

/**
 * Manual test functions
 */
function testPrivateScript() {
  console.log('=== Testing Private Script ===');

  // Test main sheet update
  console.log('Testing main sheet update...');
  const mainResult = updateMainSheetPrices();
  console.log('Main sheet result:', mainResult);

  // Test cata reports update
  console.log('Testing cata reports update...');
  const cataResult = updateCataReportsSecure();
  console.log('Cata reports result:', cataResult);

  return mainResult.success && cataResult.success;
}

/**
 * Send data to public script via webhook
 */
function sendToPublicScript(action, data) {
  // You'll need to replace this with your public script's web app URL
  const PUBLIC_SCRIPT_URL = 'https://script.google.com/macros/s/YOUR_PUBLIC_SCRIPT_ID/exec';

  try {
    const payload = {
      action: action,
      data: data,
      timestamp: new Date().toISOString()
    };

    const response = UrlFetchApp.fetch(PUBLIC_SCRIPT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    });

    if (response.getResponseCode() !== 200) {
      throw new Error(`Public script responded with ${response.getResponseCode()}: ${response.getContentText()}`);
    }

    const result = JSON.parse(response.getContentText());
    console.log('Public script response:', result);

    return result;
  } catch (error) {
    console.error('Error sending to public script:', error);
    throw error;
  }
}

/**
 * Enhanced main sheet update that sends data to public script
 */
function updateMainSheetPricesViaPublic() {
  console.log('=== Updating Main Sheet via Public Script ===');

  try {
    // Fetch prices for all realms
    const priceData = {};

    // For regular realms (spine, living, thunder, wild)
    for (const [realmKey, columnIndex] of Object.entries(REALM_COLUMNS)) {
      try {
        console.log(`Fetching price for ${realmKey}...`);

        // This would need to be implemented based on your specific API endpoints
        const price = fetchRealmPrice(realmKey);
        priceData[realmKey] = price;

        // Delay between API calls
        Utilities.sleep(API_DELAY_MS);

      } catch (error) {
        console.error(`Failed to fetch price for ${realmKey}:`, error);
        priceData[realmKey] = null; // Will show as OFF
      }
    }

    // Send data to public script
    const result = sendToPublicScript('updatePrices', priceData);

    if (result.success) {
      console.log(`✓ Successfully updated main sheet via public script: ${result.message}`);
      errorCount = 0; // Reset error count on success
    } else {
      throw new Error(result.error || 'Public script update failed');
    }

    return {
      success: true,
      message: result.message,
      data: priceData
    };

  } catch (error) {
    console.error('Error in updateMainSheetPricesViaPublic:', error);
    errorCount++;

    if (errorCount >= MAX_ERRORS_BEFORE_ALERT) {
      sendErrorAlert(error);
    }

    return {
      success: false,
      message: error.toString(),
      errorCount: errorCount
    };
  }
}

/**
 * Enhanced Cata update that sends processed data to public script
 */
function updateCataReportsViaPublic() {
  console.log('=== Updating Cata Reports via Public Script ===');

  try {
    const spreadsheet = SpreadsheetApp.openById(MAIN_SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);
    const mainSheet = spreadsheet.getSheetByName(MAIN_SHEET_NAME);

    if (!cataSheet) {
      throw new Error(`Cata sheet "${CATA_SHEET_NAME}" not found`);
    }

    // Get settings
    const pageSize = cataSheet.getRange(3, 1).getValue() || 20;
    const undercutThreshold = cataSheet.getRange(5, 1).getValue() || 0;
    const updateInterval = cataSheet.getRange(7, 1).getValue() || 60;
    const dollarRate = mainSheet.getRange(2, 1).getValue();

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in main sheet A2');
    }

    console.log(`Settings: pageSize=${pageSize}, dollarRate=${dollarRate}, threshold=${undercutThreshold}`);

    // Prepare data structure for public script
    const cataData = {
      realms: {},
      positions: {},
      timestamp: null
    };

    let totalUpdated = 0;
    let successfulRealms = 0;

    // Process each realm and prepare formatted data
    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      try {
        console.log(`Processing ${realmKey}...`);

        const realmData = fetchRealmDataSafe(realmConfig.url, pageSize);
        const formattedData = prepareRealmDataForPublic(realmData, dollarRate, undercutThreshold, cataSheet);

        cataData.realms[realmKey] = {
          column: realmConfig.column,
          values: formattedData.values,
          backgrounds: formattedData.backgrounds,
          fontWeights: formattedData.fontWeights
        };

        totalUpdated += formattedData.values.length;
        successfulRealms++;

        console.log(`✓ ${realmKey}: Prepared ${formattedData.values.length} entries`);

        // Delay between realms
        Utilities.sleep(API_DELAY_MS);

      } catch (error) {
        console.error(`✗ Error processing ${realmKey}:`, error);
      }
    }

    // Prepare timestamp
    const now = new Date();
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
    const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
    const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                      tehranTime.getMinutes().toString().padStart(2, '0');
    cataData.timestamp = timeString;

    // Prepare recommended positions
    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      if (realmConfig.recommendedUrl) {
        try {
          const position = getMibaPositionSecure(realmConfig.recommendedUrl);
          cataData.positions[realmKey] = {
            column: realmConfig.column,
            value: position || 'N/A',
            background: position ? getPositionColor(position) : '#CCCCCC',
            fontColor: position ? '#FFFFFF' : '#000000',
            fontWeight: 'bold'
          };
        } catch (error) {
          console.error(`Error getting position for ${realmKey}:`, error);
        }
      }
    }

    // Send data to public script
    const result = sendToPublicScript('updateCataData', cataData);

    if (result.success) {
      console.log(`✓ Successfully updated Cata via public script: ${result.message}`);
      errorCount = 0;

      // Setup auto update if interval > 0
      if (updateInterval > 0) {
        setupAutoUpdateSecure(updateInterval);
      }
    } else {
      throw new Error(result.error || 'Public script Cata update failed');
    }

    return {
      success: true,
      message: `Cata reports updated: ${totalUpdated} entries across ${successfulRealms}/${Object.keys(CATA_REALMS).length} realms`,
      totalUpdated: totalUpdated,
      successfulRealms: successfulRealms
    };

  } catch (error) {
    console.error('Error in updateCataReportsViaPublic:', error);
    errorCount++;

    if (errorCount >= MAX_ERRORS_BEFORE_ALERT) {
      sendErrorAlert(error);
    }

    return {
      success: false,
      message: error.toString(),
      errorCount: errorCount
    };
  }
}

/**
 * Prepare realm data for sending to public script
 */
function prepareRealmDataForPublic(realmData, dollarRate, undercutThreshold, cataSheet) {
  if (!realmData || realmData.length === 0) {
    return { values: [], backgrounds: [], fontWeights: [] };
  }

  // Get Iranian shops list
  const iranianShops = getIranianShopsSecure(cataSheet);

  // Find my shops and their prices for undercut detection
  const myShopPrices = [];
  for (const seller of realmData) {
    const shopName = (seller.username || '').toLowerCase();
    if (shopName.includes('bonyadi') || shopName.includes('miba')) {
      const usdPrice = seller.converted_unit_price || 0;
      const convertedPrice = (usdPrice * 0.91) * dollarRate;
      myShopPrices.push(convertedPrice);
    }
  }

  // Prepare formatted data
  const values = [];
  const backgrounds = [];
  const fontWeights = [];

  for (let i = 0; i < realmData.length; i++) {
    const seller = realmData[i];
    const shopName = seller.username || 'Unknown';
    const stock = seller.available_qty || 0;
    const usdPrice = seller.converted_unit_price || 0;
    const convertedPrice = (usdPrice * 0.91) * dollarRate;

    const formattedShop = shopName.substring(0, 7).padEnd(7, ' ');
    const formattedStock = formatStock(stock).padStart(6, ' ');
    const formattedUSD = usdPrice.toFixed(6);
    const formattedConverted = formatConvertedPrice(convertedPrice);

    const displayText = `${formattedShop}|${formattedStock}|${formattedUSD}|${formattedConverted}`;
    values.push([displayText]);

    let backgroundColor = null;
    let fontWeight = 'normal';
    const shopNameLower = shopName.toLowerCase();

    if (shopNameLower.includes('bonyadi') || shopNameLower.includes('miba')) {
      backgroundColor = YELLOW_COLOR;
      fontWeight = 'bold';
    } else if (isIranianShopSecure(shopName, iranianShops)) {
      backgroundColor = IRANIAN_HIGHLIGHT;
      fontWeight = 'bold';
    } else if (undercutThreshold > 0 && myShopPrices.length > 0) {
      const isUndercutting = myShopPrices.some(myPrice =>
        convertedPrice < myPrice && (myPrice - convertedPrice) <= undercutThreshold
      );
      if (isUndercutting) {
        backgroundColor = '#FFB6C1';
      }
    }

    if (!backgroundColor) {
      backgroundColor = (i % 2 === 0) ? '#FFFFFF' : LIGHT_GRAY;
    }

    backgrounds.push([backgroundColor]);
    fontWeights.push([fontWeight]);
  }

  return { values, backgrounds, fontWeights };
}

/**
 * Placeholder function for realm price fetching
 * You'll need to implement this based on your specific API endpoints
 */
function fetchRealmPrice(realmKey) {
  // This is a placeholder - implement your actual price fetching logic here
  // For now, returning null to indicate no price available
  console.log(`Fetching price for ${realmKey} - implement this function`);
  return null;
}