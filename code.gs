// g2g price updater thing

// config stuff
const SHEET_ID = '1FStcYoFPninNRsWX4XvXxHVtrc2usOnJ79R-ep3l38c';
const SHEET_NAME = 'Sheet1';
const CATA_SHEET_NAME = 'Cata';
const PRICE_ROW = 4;
const CATA_START_ROW = 6;
const REALM_COLUMNS = {
  'spine a': 2,
  'spine h': 3,
  'living a': 4,
  'living h': 5,
  'thunder a': 6,
  'thunder h': 7,
  'wild a': 8
};

// cata realms
const CATA_REALMS = {
  'firemaw a': {
    name: 'Firemaw Alliance',
    url: 'https://sls.g2g.com/offer/search?seo_term=wow-classic-gold&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41012&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41012&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 2
  },
  'gehennas h': {
    name: 'Gehennas Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41023&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41023&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 3
  },
  'golemagg h': {
    name: 'Golemagg Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41029&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41029&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 4
  },
  'everlook a': {
    name: 'Everlook Alliance',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41003&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41003&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 5
  },
  'venoxis h': {
    name: 'Venoxis Horde',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41122&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41122&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 6
  },
  'pyrewood village a': {
    name: 'Pyrewood Village Alliance',
    url: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41083&sort=lowest_price&page_size=20&group=0&currency=USD&country=TR&v=v2',
    recommendedUrl: 'https://sls.g2g.com/offer/search?service_id=lgc_service_1&brand_id=lgc_game_29076&region_id=ac3f85c1-7562-437e-b125-e89576b9a38e&filter_attr=lgc_29076_server:lgc_29076_server_41083&sort=recommended_v2&page_size=60&group=0&currency=USD&country=TR&v=v2',
    column: 7
  }
};

// colors
const GREEN_COLOR = '#00FF00';
const RED_COLOR = '#FF0000';
const YELLOW_COLOR = '#FFFF00';
const LIGHT_GRAY = '#F5F5F5';
const IRANIAN_HIGHLIGHT = '#E6B3B3'; // darker than undercut highlight
const HIGHLIGHT_SHOPS = ['bonyadi', 'miba'];

// get iranian shops from A19-A28 (max 6)
function getIranianShops() {
  try {
    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);
    const iranianShops = [];

    for (let row = 19; row <= 28; row++) {
      const shopName = sheet.getRange(row, 1).getValue();
      if (shopName && typeof shopName === 'string' && shopName.trim()) {
        iranianShops.push(shopName.trim().toLowerCase());
      }
    }

    return iranianShops;
  } catch (error) {
    console.error('error getting iranian shops:', error);
    return [];
  }
}

// typo tolerant shop matching
function isIranianShop(shopName, iranianShops) {
  if (!shopName || !iranianShops.length) return false;

  const cleanShopName = shopName.toLowerCase().trim();

  return iranianShops.some(iranianShop => {
    // exact match
    if (cleanShopName === iranianShop) return true;

    // contains match (for partial names)
    if (cleanShopName.includes(iranianShop) || iranianShop.includes(cleanShopName)) return true;

    return false;
  });
}

// get miba position in recommended results
function getMibaPosition(recommendedUrl) {
  try {
    const response = UrlFetchApp.fetch(recommendedUrl, {
      muteHttpExceptions: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (response.getResponseCode() !== 200) {
      console.error(`recommended api failed ${response.getResponseCode()}`);
      return null;
    }

    const data = JSON.parse(response.getContentText());

    if (data.code !== 2000 || !data.payload || !data.payload.results) {
      console.error('bad recommended api response');
      return null;
    }

    // find miba position (1-based)
    for (let i = 0; i < data.payload.results.length; i++) {
      const username = (data.payload.results[i].username || '').toLowerCase();
      if (username.includes('miba')) {
        return i + 1; // 1-based position
      }
    }

    return null; // miba not found

  } catch (error) {
    console.error('error getting miba position:', error);
    return null;
  }
}

// get color for position (red to green gradient, 10+ stays red)
function getPositionColor(position) {
  if (!position || position > 10) {
    return '#CC0000'; // dark red for 10+
  }

  // gradient from red (10) to green (1)
  const ratio = (10 - position) / 9; // 0 to 1, where 1 is best (position 1)

  // red to green gradient
  const red = Math.round(204 * (1 - ratio)); // 204 to 0
  const green = Math.round(204 * ratio); // 0 to 204
  const blue = 0;

  return `rgb(${red}, ${green}, ${blue})`;
}

// update recommended positions in row 4 (B4-G4)
function updateRecommendedPositions() {
  try {
    console.log('updating recommended positions...');

    const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);

    if (!cataSheet) {
      throw new Error('cata sheet not found');
    }

    // process each realm
    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      if (!realmConfig.recommendedUrl) continue;

      console.log(`getting position for ${realmKey}...`);
      const position = getMibaPosition(realmConfig.recommendedUrl);

      const cell = cataSheet.getRange(4, realmConfig.column);

      if (position) {
        cell.setValue(position);
        cell.setBackground(getPositionColor(position));
        cell.setFontColor('#FFFFFF'); // white text for contrast
        cell.setFontWeight('bold');
        console.log(`${realmKey}: position ${position}`);
      } else {
        cell.setValue('N/A');
        cell.setBackground('#CCCCCC');
        cell.setFontColor('#000000');
        console.log(`${realmKey}: miba not found`);
      }

      // small delay between requests
      Utilities.sleep(500);
    }

    console.log('recommended positions updated');
    return true;

  } catch (error) {
    console.error('error updating recommended positions:', error);
    return false;
  }
}

/**
 * Main function to handle POST requests from Chrome extension
 */
function doPost(e) {
  try {
    console.log('Received POST request');
    
    // Parse the request data
    let requestData;
    try {
      requestData = JSON.parse(e.postData.contents);
    } catch (parseError) {
      console.error('Error parsing request data:', parseError);
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Invalid JSON data'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    console.log('Request data:', requestData);
    
    if (requestData.action === 'updatePrices') {
      const result = updateSheetPrices(requestData.data);

      return ContentService
        .createTextOutput(JSON.stringify({
          success: result.success,
          message: result.message,
          timestamp: new Date().toISOString()
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }

    if (requestData.action === 'updateCataReports') {
      const result = updateCataReports();

      return ContentService
        .createTextOutput(JSON.stringify({
          success: result.success,
          message: result.message,
          timestamp: new Date().toISOString()
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Unknown action'
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Error in doPost:', error);
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle GET requests (for testing)
 */
function doGet(e) {
  return ContentService
    .createTextOutput(JSON.stringify({
      status: 'G2G Price Updater is running',
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Update sheet with price data
 */
function updateSheetPrices(priceData) {
  try {
    console.log('Updating sheet with price data:', priceData);

    // Open the spreadsheet
    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(SHEET_NAME);

    if (!sheet) {
      throw new Error(`Sheet "${SHEET_NAME}" not found`);
    }

    // Get dollar rate from A2
    const dollarRate = sheet.getRange(2, 1).getValue(); // A2
    console.log('Dollar rate from A2:', dollarRate);

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in A2. Please ensure A2 contains a valid number.');
    }

    let updatedCount = 0;
    let offCount = 0;

    // Process each realm
    for (const [realmKey, usdPrice] of Object.entries(priceData)) {
      const columnIndex = REALM_COLUMNS[realmKey.toLowerCase()];

      if (!columnIndex) {
        console.warn(`Unknown realm key: ${realmKey}`);
        continue;
      }

      const cell = sheet.getRange(PRICE_ROW, columnIndex);

      if (usdPrice !== null && usdPrice !== undefined && usdPrice > 0) {
        // Convert price: (USD price - 9%) × Dollar rate, rounded
        const discountedPrice = usdPrice * 0.91; // Subtract 9%
        const convertedPrice = Math.round(discountedPrice * dollarRate);

        cell.setValue(convertedPrice);
        cell.setBackground(GREEN_COLOR);
        cell.setFontColor('#000000'); // Black text
        updatedCount++;
        console.log(`Updated ${realmKey}: USD ${usdPrice} → ${convertedPrice} (after -9% and ×${dollarRate})`);
      } else {
        // No price available - set to OFF
        cell.setValue('OFF');
        cell.setBackground(RED_COLOR);
        cell.setFontColor('#FFFFFF'); // White text for better contrast on red
        offCount++;
        console.log(`Set ${realmKey} to OFF (no price available)`);
      }
    }
    
    // timestamp in A4 - Tehran time
    if (updatedCount > 0) {
      const now = new Date();
      const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
      const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
      const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                        tehranTime.getMinutes().toString().padStart(2, '0');

      const timestampCell = sheet.getRange(PRICE_ROW, 1);
      timestampCell.setValue(timeString);
      timestampCell.setFontSize(10);
      timestampCell.setFontColor('#000000');
      timestampCell.setBackground('#E8F5E8'); // Light green background
    }
    
    const message = `Updated ${updatedCount} prices, ${offCount} set to OFF`;
    console.log(message);
    
    return {
      success: true,
      message: message,
      updatedCount: updatedCount,
      offCount: offCount
    };
    
  } catch (error) {
    console.error('Error updating sheet:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

/**
 * Test function to manually update with sample data
 */
function testUpdate() {
  const sampleData = {
    'spine h': 0.001234,
    'spine a': null,
    'living a': 0.001456,
    'thunder a': 0.001789,
    'living h': null,
    'thunder h': 0.001890,
    'wild a': 0.001567
  };
  
  const result = updateSheetPrices(sampleData);
  console.log('Test result:', result);
  return result;
}

function setup() {
  console.log('setup done');
  try {
    const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(SHEET_NAME);
    const cataSheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);

    // make sure onSelectionChange trigger is installed
    installTriggers();

    // setup recommended positions auto update
    setupRecommendedPositionsUpdate();

    return true;
  } catch (error) {
    console.error('sheet access failed:', error);
    return false;
  }
}

// install triggers for onSelectionChange
function installTriggers() {
  const ss = SpreadsheetApp.openById(SHEET_ID);

  // delete old triggers first
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'onSelectionChange') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // install new trigger
  ScriptApp.newTrigger('onSelectionChange')
    .spreadsheet(ss)
    .onSelectionChange()
    .create();

  console.log('onSelectionChange trigger installed');
}

/**
 * Update Cata sheet with detailed realm reports
 */
function updateCataReports() {
  try {
    console.log('=== Starting Cata Reports Update ===');

    const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);

    if (!cataSheet) {
      throw new Error(`Cata sheet "${CATA_SHEET_NAME}" not found`);
    }

    // get page size from A3, default 20
    const pageSizeCell = cataSheet.getRange(3, 1);
    let pageSize = pageSizeCell.getValue();
    if (!pageSize || pageSize <= 0) {
      pageSize = 20;
      pageSizeCell.setValue(pageSize);
    }

    // get undercut threshold from A5, default 0
    const thresholdCell = cataSheet.getRange(5, 1);
    let undercutThreshold = thresholdCell.getValue();
    if (!undercutThreshold || undercutThreshold < 0) {
      undercutThreshold = 0;
      thresholdCell.setValue(undercutThreshold);
    }

    // get auto update interval from A7, min 15 seconds
    const intervalCell = cataSheet.getRange(7, 1);
    let updateInterval = intervalCell.getValue();
    if (!updateInterval || updateInterval < 15) {
      updateInterval = updateInterval && updateInterval < 15 ? 15 : 60; // default 60 if empty, min 15
      intervalCell.setValue(updateInterval);
    }

    // Get dollar rate from main sheet A2
    const mainSheet = spreadsheet.getSheetByName(SHEET_NAME);
    const dollarRate = mainSheet.getRange(2, 1).getValue();

    if (!dollarRate || dollarRate <= 0) {
      throw new Error('Invalid dollar rate in main sheet A2');
    }

    console.log(`Using page size: ${pageSize}, dollar rate: ${dollarRate}`);

    let totalUpdated = 0;

    // Process each realm - just overwrite, don't clear
    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      console.log(`Processing ${realmKey}...`);

      try {
        const realmData = fetchRealmData(realmConfig.url, pageSize);
        const updatedCount = updateRealmColumn(cataSheet, realmConfig.column, realmData, dollarRate, undercutThreshold);
        totalUpdated += updatedCount;

        console.log(`✓ ${realmKey}: Updated ${updatedCount} entries`);

        // small delay to prevent race conditions
        Utilities.sleep(100);
      } catch (error) {
        console.error(`✗ Error processing ${realmKey}:`, error);
      }
    }

    // timestamp in A1 - Tehran time (UTC+3:30)
    const now = new Date();
    // get current UTC time and add Tehran offset
    const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
    const tehranTime = new Date(utcTime + (3.5 * 60 * 60 * 1000));
    const timeString = tehranTime.getHours().toString().padStart(2, '0') + ':' +
                      tehranTime.getMinutes().toString().padStart(2, '0');

    const timestampCell = cataSheet.getRange(1, 1);
    timestampCell.setValue(timeString);
    timestampCell.setFontSize(12);
    timestampCell.setFontColor('#333333');
    timestampCell.setFontWeight('bold');

    const message = `Cata reports updated: ${totalUpdated} total entries across ${Object.keys(CATA_REALMS).length} realms`;
    console.log(message);

    // update recommended positions
    updateRecommendedPositions();

    // setup auto update if interval > 0
    if (updateInterval > 0) {
      setupAutoUpdate(updateInterval);
    }

    return {
      success: true,
      message: message,
      totalUpdated: totalUpdated
    };

  } catch (error) {
    console.error('Error updating Cata reports:', error);
    return {
      success: false,
      message: error.toString()
    };
  }
}

// Enhanced auto update setup with better error handling
function setupAutoUpdate(intervalSeconds) {
  try {
    const triggers = ScriptApp.getProjectTriggers();
    triggers.forEach(trigger => {
      if (trigger.getHandlerFunction() === 'autoUpdateCata') {
        ScriptApp.deleteTrigger(trigger);
      }
    });

    // create new trigger - convert seconds to minutes, min 1 minute
    const intervalMinutes = Math.max(1, Math.floor(intervalSeconds / 60));
    ScriptApp.newTrigger('autoUpdateCata')
      .timeBased()
      .everyMinutes(intervalMinutes)
      .create();

    console.log(`Auto-update trigger created for every ${intervalMinutes} minutes`);
  } catch (error) {
    console.error('Error setting up auto-update trigger:', error);
    // Try to continue without auto-update rather than failing completely
  }
}

// setup recommended positions auto update (every 5 minutes)
function setupRecommendedPositionsUpdate() {
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'autoUpdateRecommendedPositions') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // create trigger for every 5 minutes
  ScriptApp.newTrigger('autoUpdateRecommendedPositions')
    .timeBased()
    .everyMinutes(5)
    .create();

  console.log('recommended positions auto update setup (every 5 minutes)');
}

// auto update function for recommended positions
function autoUpdateRecommendedPositions() {
  updateRecommendedPositions();
}

// Enhanced auto update function with error recovery
function autoUpdateCata() {
  try {
    console.log('=== Auto Update Triggered ===');
    const result = updateCataReports();

    if (!result.success) {
      console.error('Auto update failed:', result.message);
      // Try to restart the trigger if it failed
      const cataSheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);
      const updateInterval = cataSheet.getRange(7, 1).getValue() || 60;
      if (updateInterval > 0) {
        setupAutoUpdate(updateInterval);
      }
    } else {
      console.log('Auto update completed successfully');
    }
  } catch (error) {
    console.error('Critical error in auto update:', error);
    // Log the error but don't crash - let the trigger try again next time
  }
}

// handle cell selection for undercut copying - triggers when you select cells
function onSelectionChange(e) {
  try {
    console.log('onSelectionChange triggered');

    if (!e || !e.source || !e.range) {
      console.log('missing event data');
      return;
    }

    const sheet = e.source.getActiveSheet();
    const range = e.range;

    console.log(`sheet: ${sheet.getName()}, row: ${range.getRow()}, col: ${range.getColumn()}`);

    // only handle clicks on Cata sheet
    if (sheet.getName() !== CATA_SHEET_NAME) {
      console.log('not cata sheet');
      return;
    }

    // only handle single cell selection starting from row 3
    if (range.getNumRows() !== 1 || range.getNumColumns() !== 1 || range.getRow() < 3) {
      console.log('wrong cell selection');
      return;
    }

    const cellValue = range.getValue();
    console.log(`cell value: ${cellValue}`);

    if (!cellValue || typeof cellValue !== 'string') {
      console.log('no string value');
      return;
    }

    // check if it's a formatted price cell (contains |)
    if (!cellValue.includes('|')) {
      console.log('no pipe in cell');
      return;
    }

    // extract USD price from format: "ShopName|Stock|USDPrice|ConvertedPrice"
    const parts = cellValue.split('|');
    if (parts.length !== 4) {
      console.log(`wrong parts count: ${parts.length}`);
      return;
    }

    const usdPrice = parseFloat(parts[2]);
    if (isNaN(usdPrice)) {
      console.log(`bad usd price: ${parts[2]}`);
      return;
    }

    // calculate undercut price (reduce by 0.000001)
    const undercutPrice = (usdPrice - 0.000001).toFixed(6);
    console.log(`undercut price: ${undercutPrice}`);

    // put undercut price in A9
    const copyCell = sheet.getRange(9, 1);
    copyCell.setValue(undercutPrice);
    copyCell.setBackground('#90EE90');
    copyCell.setFontWeight('bold');

    console.log('A9 updated successfully');

  } catch (error) {
    console.error('Error in onSelectionChange:', error);
  }
}

// manual function to test undercut copying
function testUndercutCopy() {
  const sheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);
  const testPrice = 0.610000;
  const undercutPrice = (testPrice - 0.000001).toFixed(6);

  const copyCell = sheet.getRange(9, 1);
  copyCell.setValue(undercutPrice);
  copyCell.setBackground('#90EE90');
  copyCell.setFontWeight('bold');

  console.log(`Test: ${testPrice} -> ${undercutPrice} (placed in A9)`);
}

function onEdit(e) {
  // dont need this anymore
}

// simple function to run cata report - can be called from menu or button
function runCataReport() {
  fetchCataData();
}

// test recommended positions manually
function testRecommendedPositions() {
  updateRecommendedPositions();
}

// clear all cata columns manually (for debugging)
function clearAllCataColumns() {
  try {
    const cataSheet = SpreadsheetApp.openById(SHEET_ID).getSheetByName(CATA_SHEET_NAME);

    // clear all realm columns
    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      const clearRange = cataSheet.getRange(CATA_START_ROW, realmConfig.column, 100, 1);
      clearRange.clearContent();
      clearRange.clearFormat();
      console.log(`Cleared column ${realmConfig.column} for ${realmKey}`);
    }

    console.log('All cata columns cleared');
  } catch (error) {
    console.error('Error clearing columns:', error);
  }
}

// force refresh with step-by-step updates (for debugging)
function forceRefreshStepByStep() {
  try {
    console.log('=== Force Refresh Step by Step ===');

    const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
    const cataSheet = spreadsheet.getSheetByName(CATA_SHEET_NAME);
    const mainSheet = spreadsheet.getSheetByName(SHEET_NAME);

    const dollarRate = mainSheet.getRange(2, 1).getValue();
    const pageSize = cataSheet.getRange(3, 1).getValue() || 20;
    const undercutThreshold = cataSheet.getRange(5, 1).getValue() || 0;

    console.log(`Settings: pageSize=${pageSize}, dollarRate=${dollarRate}, threshold=${undercutThreshold}`);

    // update one by one with longer delays
    for (const [realmKey, realmConfig] of Object.entries(CATA_REALMS)) {
      console.log(`\n--- Processing ${realmKey} ---`);

      try {
        const realmData = fetchRealmData(realmConfig.url, pageSize);
        console.log(`Fetched ${realmData ? realmData.length : 0} entries for ${realmKey}`);

        const updatedCount = updateRealmColumn(cataSheet, realmConfig.column, realmData, dollarRate, undercutThreshold);
        console.log(`Updated ${updatedCount} entries for ${realmKey}`);

        // longer delay between realms
        Utilities.sleep(1000);
      } catch (error) {
        console.error(`Error with ${realmKey}:`, error);
      }
    }

    console.log('=== Force Refresh Complete ===');
  } catch (error) {
    console.error('Force refresh failed:', error);
  }
}

/**
 * Fetch data from G2G API for a specific realm
 */
function fetchRealmData(url, pageSize = 20) {
  console.log(`Fetching data from ${url} with page size ${pageSize}`);

  // Add page_size parameter to URL if not already present
  if (!url.includes('page_size=')) {
    url += (url.includes('?') ? '&' : '?') + `page_size=${pageSize}`;
  } else {
    // Replace existing page_size parameter
    url = url.replace(/page_size=\d+/, `page_size=${pageSize}`);
  }

  // Enhanced error handling with retries
  let lastError;
  for (let attempt = 1; attempt <= 3; attempt++) {
    try {
      console.log(`Fetch attempt ${attempt}/3 for ${url.substring(0, 50)}...`);

      const response = UrlFetchApp.fetch(url, {
        muteHttpExceptions: true,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      if (response.getResponseCode() !== 200) {
        throw new Error(`API request failed with status ${response.getResponseCode()}: ${response.getContentText()}`);
      }

      const data = JSON.parse(response.getContentText());

      if (data.code !== 2000 || !data.payload || !data.payload.results) {
        throw new Error(`Invalid API response format: code=${data.code}, payload=${!!data.payload}`);
      }

      // sort by converted_unit_price only
      data.payload.results.sort((a, b) => {
        const priceA = a.converted_unit_price || 999;
        const priceB = b.converted_unit_price || 999;
        return priceA - priceB;
      });

      console.log(`Successfully fetched ${data.payload.results.length} results on attempt ${attempt}`);
      return data.payload.results;

    } catch (error) {
      lastError = error;
      console.error(`Fetch attempt ${attempt} failed:`, error);

      if (attempt < 3) {
        // Wait before retry, increasing delay each time
        Utilities.sleep(1000 * attempt);
      }
    }
  }

  // If all attempts failed, throw the last error
  console.error('All fetch attempts failed');
  throw lastError;
}

// format stock with K/M - 1500 becomes 1.5K, 2000000 becomes 2.0M (8 chars max)
function formatStock(stock) {
  if (stock >= 1000000) {
    return (stock / 1000000).toFixed(1) + 'M';
  } else if (stock >= 1000) {
    return (stock / 1000).toFixed(1) + 'K';
  } else {
    return stock.toString();
  }
}

// convert price to K format - 46705 becomes 46.7K
function formatConvertedPrice(price) {
  return (price / 1000).toFixed(1) + 'K';
}

// update realm column with data - does all the formatting and highlighting
function updateRealmColumn(sheet, column, realmData, dollarRate, undercutThreshold = 0) {
  const startRow = CATA_START_ROW;

  if (!realmData || realmData.length === 0) {
    console.log(`No data for column ${column}`);
    return 0;
  }

  console.log(`Updating column ${column} with ${realmData.length} entries`);

  // get iranian shops list
  const iranianShops = getIranianShops();

  // find my shops and their prices for undercut detection
  const myShopPrices = [];
  for (const seller of realmData) {
    const shopName = (seller.username || '').toLowerCase();
    if (shopName.includes('bonyadi') || shopName.includes('miba')) {
      const usdPrice = seller.converted_unit_price || 0;
      const convertedPrice = (usdPrice * 0.91) * dollarRate;
      myShopPrices.push(convertedPrice);
    }
  }

  // prepare batch data
  const values = [];
  const backgrounds = [];
  const fontWeights = [];

  for (let i = 0; i < realmData.length; i++) {
    const seller = realmData[i];
    const shopName = seller.username || 'Unknown';
    const stock = seller.available_qty || 0;
    const usdPrice = seller.converted_unit_price || 0;
    const convertedPrice = (usdPrice * 0.91) * dollarRate;

    const formattedShop = shopName.substring(0, 7).padEnd(7, ' ');
    const formattedStock = formatStock(stock).padStart(6, ' ');
    const formattedUSD = usdPrice.toFixed(6);
    const formattedConverted = formatConvertedPrice(convertedPrice);

    const displayText = `${formattedShop}|${formattedStock}|${formattedUSD}|${formattedConverted}`;
    values.push([displayText]);

    let backgroundColor = null;
    let fontWeight = 'normal';
    const shopNameLower = shopName.toLowerCase();

    if (shopNameLower.includes('bonyadi') || shopNameLower.includes('miba')) {
      backgroundColor = YELLOW_COLOR;
      fontWeight = 'bold';
    } else if (isIranianShop(shopName, iranianShops)) {
      backgroundColor = IRANIAN_HIGHLIGHT;
      fontWeight = 'bold';
    } else if (undercutThreshold > 0 && myShopPrices.length > 0) {
      const isUndercutting = myShopPrices.some(myPrice =>
        convertedPrice < myPrice && (myPrice - convertedPrice) <= undercutThreshold
      );
      if (isUndercutting) {
        backgroundColor = '#FFB6C1';
      }
    }

    if (!backgroundColor) {
      backgroundColor = (i % 2 === 0) ? '#FFFFFF' : LIGHT_GRAY;
    }

    backgrounds.push([backgroundColor]);
    fontWeights.push([fontWeight]);
  }

  if (values.length > 0) {
    try {
      const dataRange = sheet.getRange(startRow, column, values.length, 1);
      dataRange.setValues(values);
      dataRange.setBackgrounds(backgrounds);
      dataRange.setFontWeights(fontWeights);
      dataRange.setFontFamily('Consolas');
      dataRange.setFontSize(9);
      dataRange.setVerticalAlignment('middle');
      dataRange.setHorizontalAlignment('left');
      dataRange.setFontColor('#000000');
      dataRange.setBorder(true, true, true, true, true, true, '#CCCCCC', SpreadsheetApp.BorderStyle.SOLID);

      // make converted prices green
      for (let i = 0; i < values.length; i++) {
        const cellRange = sheet.getRange(startRow + i, column);
        const text = values[i][0];
        const parts = text.split('|');
        if (parts.length === 4) {
          const richText = SpreadsheetApp.newRichTextValue()
            .setText(text)
            .setTextStyle(0, text.lastIndexOf('|'), SpreadsheetApp.newTextStyle().setForegroundColor('#000000').build())
            .setTextStyle(text.lastIndexOf('|') + 1, text.length, SpreadsheetApp.newTextStyle().setForegroundColor('#008000').build())
            .build();
          cellRange.setRichTextValue(richText);
        }
      }

      console.log(`Successfully updated ${values.length} entries in column ${column}`);

      // clear any old data below the new data (in case new data is shorter)
      const clearStartRow = startRow + values.length;
      const clearEndRow = startRow + 100; // clear up to row 100
      if (clearStartRow <= clearEndRow) {
        const clearRange = sheet.getRange(clearStartRow, column, clearEndRow - clearStartRow + 1, 1);
        clearRange.clearContent();
        clearRange.clearFormat();
      }

    } catch (error) {
      console.error(`Error updating column ${column}:`, error);
      throw error;
    }
  }

  return realmData.length;
}

// debug api stuff
function debugApiResponse() {
  try {
    const testUrl = CATA_REALMS['firemaw a'].url;
    const response = UrlFetchApp.fetch(testUrl, {
      muteHttpExceptions: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (response.getResponseCode() !== 200) {
      console.error(`api failed ${response.getResponseCode()}`);
      return;
    }

    const data = JSON.parse(response.getContentText());

    if (data.code !== 2000 || !data.payload || !data.payload.results) {
      console.error('bad api response');
      return;
    }

    console.log(`got ${data.payload.results.length} results`);

    data.payload.results.slice(0, 5).forEach((seller, index) => {
      console.log(`${index + 1}. ${seller.username}: converted_unit_price=${seller.converted_unit_price}, unit_price_in_usd=${seller.unit_price_in_usd}`);
    });

    return data.payload.results.slice(0, 5);

  } catch (error) {
    console.error('debug failed:', error);
    return null;
  }
}



